\documentclass[a4paper]{article}

\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{enumitem}

% Definir los entornos necesarios para exams
\newenvironment{question}{}{}
\newenvironment{solution}{}{}
\newenvironment{answerlist}{\begin{enumerate}}{\end{enumerate}}

\begin{document}
\SweaveOpts{concordance=TRUE}

<<echo=FALSE, results=hide>>=
# Configuracion para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# CONFIGURACION RADICAL ANTI-NOTACION CIENTIFICA
options(scipen = 999)  # Evitar notacion cientifica completamente
options(digits = 10)   # Suficientes digitos para numeros grandes

library(exams)

# Funcion para formatear enteros sin notacion cientifica
formatear_entero <- function(numero) {
  # Forzar formato entero sin notacion cientifica JAMAS
  formatC(as.numeric(numero), format = "d", big.mark = "")
}

# Generar datos aleatorios simples
contextos <- c("cine", "teatro", "concierto", "parque")
productos1 <- c("boleta", "entrada", "ticket", "acceso")
productos2 <- c("palomitas", "bebida", "snack", "refresco")

contexto_idx <- sample(1:4, 1)
contexto <- contextos[contexto_idx]
producto1 <- productos1[contexto_idx]
producto2 <- productos2[contexto_idx]

# Presupuesto total fijo
presupuesto_total <- sample(c(56000, 64000, 72000, 80000), 1)

# Generar 8 puntos de datos con relacion lineal decreciente
costos_producto1 <- c(0, 8000, 16000, 24000, 32000, 40000, 48000, 56000)
costos_producto2 <- presupuesto_total - costos_producto1

# Asegurar valores positivos
costos_producto2 <- pmax(costos_producto2, 1000)

# Variables para mostrar
var_x <- paste("Costo de", producto1, "(x)")
var_y <- paste("Costo de", producto2, "(y)")

# Opciones de respuesta
opciones <- c(
  "La grafica que representa la funcion es A, porque muestra una relacion constante entre las variables.",
  "La grafica que representa la funcion es B, porque muestra una relacion lineal decreciente entre las variables.",
  "La grafica que representa la funcion es C, porque representa una funcion escalonada apropiada.",
  "La grafica que representa la funcion es D, porque los puntos dispersos muestran la variabilidad real."
)

# Vector de soluciones (B es la correcta)
solutions <- c(FALSE, TRUE, FALSE, FALSE)

# Explicaciones
explicaciones <- c(
  "Falso. La grafica A muestra una relacion constante, no la relacion inversa esperada.",
  "Verdadero. La grafica B representa correctamente la relacion lineal decreciente entre las variables.",
  "Falso. La grafica C muestra una funcion escalonada, no una relacion continua.",
  "Falso. La grafica D muestra puntos dispersos sin conexion, no una funcion definida."
)
@

\begin{question}

En un \Sexpr{contexto}, el presupuesto total disponible para entretenimiento es de $\Sexpr{formatear_entero(presupuesto_total)}$. Este presupuesto se distribuye entre el costo de \Sexpr{producto1} y el costo de \Sexpr{producto2}, de tal manera que cuando aumenta el gasto en uno, disminuye proporcionalmente el gasto en el otro.

La siguiente tabla muestra la relacion entre estas dos variables:

% Crear tabla de datos simple
\begin{center}
\begin{tabular}{|c|c|}
\hline
\textbf{\Sexpr{var_x}} & \textbf{\Sexpr{var_y}} \\
\hline
\Sexpr{formatear_entero(costos_producto1[1])} & \Sexpr{formatear_entero(costos_producto2[1])} \\
\hline
\Sexpr{formatear_entero(costos_producto1[2])} & \Sexpr{formatear_entero(costos_producto2[2])} \\
\hline
\Sexpr{formatear_entero(costos_producto1[3])} & \Sexpr{formatear_entero(costos_producto2[3])} \\
\hline
\Sexpr{formatear_entero(costos_producto1[4])} & \Sexpr{formatear_entero(costos_producto2[4])} \\
\hline
\Sexpr{formatear_entero(costos_producto1[5])} & \Sexpr{formatear_entero(costos_producto2[5])} \\
\hline
\Sexpr{formatear_entero(costos_producto1[6])} & \Sexpr{formatear_entero(costos_producto2[6])} \\
\hline
\Sexpr{formatear_entero(costos_producto1[7])} & \Sexpr{formatear_entero(costos_producto2[7])} \\
\hline
\Sexpr{formatear_entero(costos_producto1[8])} & \Sexpr{formatear_entero(costos_producto2[8])} \\
\hline
\end{tabular}
\end{center}

La grafica que representa esta funcion es:

% Mostrar las 4 opciones graficas como imagenes existentes
\begin{center}
\begin{tabular}{cc}
\textbf{A.} & \textbf{B.} \\
\includegraphics[width=5cm]{01.png} & \includegraphics[width=5cm]{02.png} \\
\textbf{C.} & \textbf{D.} \\
\includegraphics[width=5cm]{03.png} & \includegraphics[width=5cm]{04.png} \\
\end{tabular}
\end{center}

<<echo=FALSE, results=tex>>=
answerlist(opciones)
@

\end{question}

\begin{solution}

Para resolver este problema, debemos analizar la relacion entre las dos variables presentadas en la tabla.

Observando los datos:
- Cuando el \Sexpr{producto1} cuesta $\Sexpr{formatear_entero(costos_producto1[1])}$, el \Sexpr{producto2} cuesta $\Sexpr{formatear_entero(costos_producto2[1])}$
- Cuando el \Sexpr{producto1} cuesta $\Sexpr{formatear_entero(costos_producto1[8])}$, el \Sexpr{producto2} cuesta $\Sexpr{formatear_entero(costos_producto2[8])}$

Esto muestra una relacion lineal decreciente: a medida que aumenta el costo de \Sexpr{producto1}, disminuye el costo de \Sexpr{producto2}, manteniendo un presupuesto total constante de $\Sexpr{formatear_entero(presupuesto_total)}$.

<<echo=FALSE, results=tex>>=
answerlist(explicaciones)
@

\end{solution}

%% META-INFORMATION
\exname{Costo Boleta Interpretacion Representacion}
\extype{schoice}
\exsolution{\Sexpr{mchoice2string(solutions)}}
\exshuffle{TRUE}
\exsection{Interpretacion y Representacion}

\end{document}
