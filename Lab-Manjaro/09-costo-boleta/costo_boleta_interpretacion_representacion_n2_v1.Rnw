\documentclass[a4paper]{article}

\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{enumitem}

% Definir los entornos necesarios para exams
\newenvironment{question}{}{}
\newenvironment{solution}{}{}
\newenvironment{answerlist}{\begin{enumerate}}{\end{enumerate}}

\begin{document}
\SweaveOpts{concordance=TRUE}

\begin{enumerate}

<<echo=FALSE, results=hide>>=
# Configuracion para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# CONFIGURACION RADICAL ANTI-NOTACION CIENTIFICA
options(scipen = 999)  # Evitar notacion cientifica completamente
options(digits = 10)   # Suficientes digitos para numeros grandes

library(exams)

# Funcion para formatear enteros sin notacion cientifica
formatear_entero <- function(numero) {
  # Forzar formato entero sin notacion cientifica JAMAS
  formatC(as.numeric(numero), format = "d", big.mark = "")
}

# Funcion generar_datos() - Sistema avanzado con 300+ versiones unicas
generar_datos <- function() {
  # Contextos aleatorios (8 escenarios diferentes)
  contextos <- list(
    list(lugar = "cine", producto1 = "boleta", producto2 = "palomitas"),
    list(lugar = "teatro", producto1 = "entrada", producto2 = "bebida"),
    list(lugar = "concierto", producto1 = "ticket", producto2 = "snack"),
    list(lugar = "parque", producto1 = "acceso", producto2 = "refresco"),
    list(lugar = "museo", producto1 = "boleto", producto2 = "souvenir"),
    list(lugar = "estadio", producto1 = "entrada", producto2 = "comida"),
    list(lugar = "festival", producto1 = "pase", producto2 = "bebida"),
    list(lugar = "circo", producto1 = "boleta", producto2 = "dulces")
  )

  contexto <- sample(contextos, 1)[[1]]

  # Parametros numericos variables con relacion lineal decreciente
  # Presupuesto total fijo
  presupuesto_total <- sample(c(56000, 64000, 72000, 80000, 88000, 96000), 1)

  # Precio unitario del producto2 (base)
  precio_base_producto2 <- sample(c(1000, 1250, 1500, 2000, 2500), 1)

  # Generar 8 puntos de datos manteniendo relacion lineal
  costos_producto1 <- seq(0, presupuesto_total * 0.7, length.out = 8)
  costos_producto1 <- round(costos_producto1 / 1000) * 1000  # Redondear a miles

  # Calcular costos correspondientes del producto2 (relacion inversa)
  costos_producto2 <- presupuesto_total - costos_producto1

  # Asegurar que los valores sean realistas
  costos_producto2 <- pmax(costos_producto2, precio_base_producto2)

  # Colores aleatorios para las graficas
  colores <- sample(c("blue", "red", "green", "purple", "orange", "brown"), 4, replace = FALSE)

  # Nombres de variables aleatorios
  var_x <- paste("Costo de", contexto$producto1, "(x)")
  var_y <- paste("Costo de", contexto$producto2, "(y)")

  # Generar opciones de respuesta con sistema avanzado de distractores
  permitir_valores_duplicados <- sample(c(TRUE, FALSE), 1, prob = c(0.3, 0.7))

  # Opcion correcta (B - linea decreciente)
  opcion_correcta <- paste0("La grafica que representa la funcion es B, porque muestra una relacion lineal decreciente entre ",
                           contexto$producto1, " y ", contexto$producto2, ", donde a mayor costo de ",
                           contexto$producto1, " corresponde menor costo de ", contexto$producto2, ".")

  # Distractores (8+ tipos diferentes)
  distractores <- c(
    paste0("La grafica que representa la funcion es A, porque muestra una relacion constante entre las variables."),
    paste0("La grafica que representa la funcion es C, porque representa una funcion escalonada apropiada."),
    paste0("La grafica que representa la funcion es D, porque los puntos dispersos muestran la variabilidad real."),
    paste0("La grafica que representa la funcion es A, porque la linea horizontal indica equilibrio entre costos."),
    paste0("La grafica que representa la funcion es C, porque los escalones representan cambios discretos de precio."),
    paste0("La grafica que representa la funcion es D, porque cada punto representa una compra independiente."),
    paste0("La grafica que representa la funcion es A, porque no hay relacion entre las variables."),
    paste0("La grafica que representa la funcion es C, porque muestra incrementos graduales de costo.")
  )

  # Seleccionar 3 distractores
  distractores_seleccionados <- sample(distractores, 3)

  # Crear opciones finales
  opciones <- c(distractores_seleccionados[1], opcion_correcta, distractores_seleccionados[2:3])

  # Verificar unicidad textual
  if(length(unique(opciones)) != 4) {
    # Regenerar si hay duplicados
    return(generar_datos())
  }

  # Vector de soluciones (TRUE para la opcion correcta)
  solutions <- c(FALSE, TRUE, FALSE, FALSE)

  # Explicaciones para cada opcion
  explicaciones <- c(
    "Falso. La grafica A muestra una relacion constante, no la relacion inversa esperada.",
    "Verdadero. La grafica B representa correctamente la relacion lineal decreciente entre las variables.",
    "Falso. La grafica C muestra una funcion escalonada, no una relacion continua.",
    "Falso. La grafica D muestra puntos dispersos sin conexion, no una funcion definida."
  )

  return(list(
    contexto = contexto,
    presupuesto_total = presupuesto_total,
    costos_producto1 = costos_producto1,
    costos_producto2 = costos_producto2,
    var_x = var_x,
    var_y = var_y,
    colores = colores,
    opciones = opciones,
    solutions = solutions,
    explicaciones = explicaciones,
    opcion_correcta = opcion_correcta
  ))
}

# Generar datos para este ejercicio
datos <- generar_datos()
@



\begin{question}

En un \Sexpr{datos$contexto$lugar}, el presupuesto total disponible para entretenimiento es de $\Sexpr{formatear_entero(datos$presupuesto_total)}$. Este presupuesto se distribuye entre el costo de \Sexpr{datos$contexto$producto1} y el costo de \Sexpr{datos$contexto$producto2}, de tal manera que cuando aumenta el gasto en uno, disminuye proporcionalmente el gasto en el otro.

La siguiente tabla muestra la relacion entre estas dos variables:

% Crear tabla de datos simple
\begin{center}
\begin{tabular}{|c|c|}
\hline
\textbf{\Sexpr{datos$var_x}} & \textbf{\Sexpr{datos$var_y}} \\
\hline
\Sexpr{formatear_entero(datos$costos_producto1[1])} & \Sexpr{formatear_entero(datos$costos_producto2[1])} \\
\hline
\Sexpr{formatear_entero(datos$costos_producto1[2])} & \Sexpr{formatear_entero(datos$costos_producto2[2])} \\
\hline
\Sexpr{formatear_entero(datos$costos_producto1[3])} & \Sexpr{formatear_entero(datos$costos_producto2[3])} \\
\hline
\Sexpr{formatear_entero(datos$costos_producto1[4])} & \Sexpr{formatear_entero(datos$costos_producto2[4])} \\
\hline
\Sexpr{formatear_entero(datos$costos_producto1[5])} & \Sexpr{formatear_entero(datos$costos_producto2[5])} \\
\hline
\Sexpr{formatear_entero(datos$costos_producto1[6])} & \Sexpr{formatear_entero(datos$costos_producto2[6])} \\
\hline
\Sexpr{formatear_entero(datos$costos_producto1[7])} & \Sexpr{formatear_entero(datos$costos_producto2[7])} \\
\hline
\Sexpr{formatear_entero(datos$costos_producto1[8])} & \Sexpr{formatear_entero(datos$costos_producto2[8])} \\
\hline
\end{tabular}
\end{center}

La grafica que representa esta funcion es:

% Mostrar las 4 opciones graficas como imagenes existentes
\begin{center}
\begin{tabular}{cc}
\textbf{A.} & \textbf{B.} \\
\includegraphics[width=5cm]{01.png} & \includegraphics[width=5cm]{02.png} \\
\textbf{C.} & \textbf{D.} \\
\includegraphics[width=5cm]{03.png} & \includegraphics[width=5cm]{04.png} \\
\end{tabular}
\end{center}

<<echo=FALSE, results=tex>>=
answerlist(datos$opciones)
@

\end{question}

\begin{solution}

Para resolver este problema, debemos analizar la relacion entre las dos variables presentadas en la tabla.

Observando los datos:
- Cuando el \Sexpr{datos$contexto$producto1} cuesta $\Sexpr{formatear_entero(datos$costos_producto1[1])}$, el \Sexpr{datos$contexto$producto2} cuesta $\Sexpr{formatear_entero(datos$costos_producto2[1])}$
- Cuando el \Sexpr{datos$contexto$producto1} cuesta $\Sexpr{formatear_entero(datos$costos_producto1[length(datos$costos_producto1)])}$, el \Sexpr{datos$contexto$producto2} cuesta $\Sexpr{formatear_entero(datos$costos_producto2[length(datos$costos_producto2)])}$

Esto muestra una relacion lineal decreciente: a medida que aumenta el costo de \Sexpr{datos$contexto$producto1}, disminuye el costo de \Sexpr{datos$contexto$producto2}, manteniendo un presupuesto total constante de $\Sexpr{formatear_entero(datos$presupuesto_total)}$.

<<echo=FALSE, results=tex>>=
answerlist(datos$explicaciones)
@

\end{solution}

%% META-INFORMATION
\exname{Costo Boleta Interpretacion Representacion}
\extype{schoice}
\exsolution{\Sexpr{mchoice2string(datos$solutions)}}
\exshuffle{TRUE}
\exsection{Interpretacion y Representacion}

\end{enumerate}
\end{document}
